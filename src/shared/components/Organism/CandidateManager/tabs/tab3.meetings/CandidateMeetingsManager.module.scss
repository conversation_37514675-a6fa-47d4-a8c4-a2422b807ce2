@import '/src/shared/theme/theme.scss';

.tabsContainer {
  display: flex;
  flex-direction: row;
  gap: var(--8_12);
  padding: var(--16_20) var(--16_8);
  padding-bottom: 0;
}

.tabButton {
  width: fit-content;
  background-color: colors(brand_10);
  * {
    color: colors(brand);
  }
  border-radius: variables(gutter);
  &.activeTab {
    background-color: colors(brand);
    * {
      color: colors(white);
    }
  }
}

.meetingsContainer {
  flex: 1;
  padding-bottom: 0;
  gap: var(--8_12);
}

.emptyResult {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}

.createButton {
  width: 100%;
}

@layer organism {
  .scrollArea {
    padding: variables(largeGutter);
    gap: variables(largeGutter);
    overflow: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
